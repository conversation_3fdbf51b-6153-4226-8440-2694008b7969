name: 自動化測試

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  unit-tests:
    name: 單元測試
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代碼
      uses: actions/checkout@v4
      
    - name: 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安裝依賴
      run: npm ci
      
    - name: 執行 ESLint
      run: npm run lint
      
    - name: 執行單元測試
      run: npm run test:unit
      env:
        # 測試環境變數
        PAYUNI_ENVIRONMENT: sandbox
        PAYUNI_SANDBOX_MERCHANT_ID: ${{ secrets.PAYUNI_SANDBOX_MERCHANT_ID }}
        PAYUNI_SANDBOX_HASH_KEY: ${{ secrets.PAYUNI_SANDBOX_HASH_KEY }}
        PAYUNI_SANDBOX_HASH_IV: ${{ secrets.PAYUNI_SANDBOX_HASH_IV }}
        
    - name: 上傳測試覆蓋率報告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        fail_ci_if_error: false

  integration-tests:
    name: 整合測試
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代碼
      uses: actions/checkout@v4
      
    - name: 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安裝依賴
      run: npm ci
      
    - name: 執行整合測試
      run: npm run test:integration
      env:
        # 測試環境變數
        PAYUNI_ENVIRONMENT: sandbox
        PAYUNI_SANDBOX_MERCHANT_ID: ${{ secrets.PAYUNI_SANDBOX_MERCHANT_ID }}
        PAYUNI_SANDBOX_HASH_KEY: ${{ secrets.PAYUNI_SANDBOX_HASH_KEY }}
        PAYUNI_SANDBOX_HASH_IV: ${{ secrets.PAYUNI_SANDBOX_HASH_IV }}
        GOOGLE_SERVICE_ACCOUNT_EMAIL: ${{ secrets.GOOGLE_SERVICE_ACCOUNT_EMAIL }}
        GOOGLE_PRIVATE_KEY: ${{ secrets.GOOGLE_PRIVATE_KEY }}
        GOOGLE_SHEET_ID: ${{ secrets.TEST_GOOGLE_SHEET_ID }}
        META_PIXEL_ID: ${{ secrets.META_PIXEL_ID }}
        META_ACCESS_TOKEN: ${{ secrets.META_ACCESS_TOKEN }}



  build-test:
    name: 建置測試
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代碼
      uses: actions/checkout@v4
      
    - name: 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安裝依賴
      run: npm ci
      
    - name: 測試建置
      run: npm run build
      env:
        PAYUNI_ENVIRONMENT: sandbox
        PAYUNI_SANDBOX_MERCHANT_ID: test_merchant_id
        PAYUNI_SANDBOX_HASH_KEY: test_hash_key
        PAYUNI_SANDBOX_HASH_IV: test_hash_iv
        GOOGLE_SERVICE_ACCOUNT_EMAIL: <EMAIL>
        GOOGLE_PRIVATE_KEY: test_private_key
        GOOGLE_SHEET_ID: test_sheet_id
