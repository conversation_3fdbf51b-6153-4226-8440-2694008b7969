/**
 * Event Registration API 路由測試
 * 測試 /api/event-registration 端點的完整功能
 */

import { NextRequest } from 'next/server';
import { POST } from '@/app/api/event-registration/route';

// Mock dependencies
jest.mock('@/lib/google-sheets', () => ({
  appendToSheet: jest.fn(),
}));

jest.mock('@/lib/tracking-manager', () => ({
  createTrackingManager: jest.fn(() => ({
    trackInitiateCheckout: jest.fn(),
  })),
}));

// Mock console methods to reduce test noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
});

describe('/api/event-registration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock environment variables
    process.env.PAYUNI_ENVIRONMENT = 'sandbox';
    process.env.NEXT_PUBLIC_BASE_URL = 'http://localhost:3000';
  });

  const validRegistrationData = {
    eventName: '錶匠體驗機芯拆解',
    eventPrice: 1500,
    sessionTimes: ['台北 07/20（日）13:20'],
    customTimeLocation: '',
    participationType: 'individual',
    name: '測試用戶',
    email: '<EMAIL>',
    phone: '0912345678',
    companionName: '',
    companionEmail: '',
    gender: 'male',
    age: '30-39',
    region: 'north',
    watchTypes: ['機械錶'],
    watchBrands: 'Rolex',
    questions: '測試問題',
    agreeToTerms: true,
    submittedAt: new Date().toISOString(),
    needsPayment: true,
    utmParams: {
      utm_campaign: 'test_campaign',
      utm_source_platform: 'facebook',
      utm_marketing_tactic: 'social_media',
      utm_creative_format: 'image'
    }
  };

  describe('成功案例', () => {
    test('應該成功處理個人報名', async () => {
      const { appendToSheet } = require('@/lib/google-sheets');
      appendToSheet.mockResolvedValue(true);

      const request = new NextRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: JSON.stringify(validRegistrationData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.orderNo).toMatch(/^pangea_\d+$/);
      expect(data.needsPayment).toBe(true);
      expect(data.message).toContain('請繼續完成付款');
      expect(appendToSheet).toHaveBeenCalled();
    });

    test('應該成功處理雙人團報', async () => {
      const { appendToSheet } = require('@/lib/google-sheets');
      appendToSheet.mockResolvedValue(true);

      const pairData = {
        ...validRegistrationData,
        participationType: 'pair',
        companionName: '同行者',
        companionEmail: '<EMAIL>',
        eventPrice: 3000,
      };

      const request = new NextRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: JSON.stringify(pairData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(appendToSheet).toHaveBeenCalled();
    });

    test('應該成功處理自訂時間地點報名', async () => {
      const { appendToSheet } = require('@/lib/google-sheets');
      appendToSheet.mockResolvedValue(true);

      const customTimeData = {
        ...validRegistrationData,
        sessionTimes: ['有意願但無合適時間地點'],
        customTimeLocation: '高雄平日晚上',
        needsPayment: false,
        eventPrice: 0,
      };

      const request = new NextRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: JSON.stringify(customTimeData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.needsPayment).toBe(false);
      expect(data.message).toContain('報名成功');
    });
  });

  describe('驗證錯誤', () => {
    test('應該拒絕缺少必填欄位的請求', async () => {
      const incompleteData = {
        ...validRegistrationData,
        name: '', // 缺少姓名
      };

      const request = new NextRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: JSON.stringify(incompleteData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('缺少必填欄位');
    });

    test('應該拒絕雙人團報但缺少同行者資訊', async () => {
      const invalidPairData = {
        ...validRegistrationData,
        participationType: 'pair',
        companionName: '', // 缺少同行者姓名
        companionEmail: '',
      };

      const request = new NextRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: JSON.stringify(invalidPairData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('雙人團報需要填寫同行者資訊');
    });

    test('應該拒絕選擇自訂時間但未填寫地點', async () => {
      const invalidCustomData = {
        ...validRegistrationData,
        sessionTimes: ['有意願但無合適時間地點'],
        customTimeLocation: '', // 缺少自訂時間地點
      };

      const request = new NextRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: JSON.stringify(invalidCustomData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('請填寫您期待的時間與地區');
    });
  });

  describe('錯誤處理', () => {
    test('應該處理 Google Sheets 寫入失敗', async () => {
      const { appendToSheet } = require('@/lib/google-sheets');
      appendToSheet.mockRejectedValue(new Error('Google Sheets API 錯誤'));

      const request = new NextRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: JSON.stringify(validRegistrationData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('伺服器錯誤');
    });

    test('應該處理無效的 JSON 請求', async () => {
      const request = new NextRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('伺服器錯誤');
    });
  });

  describe('UTM 參數追蹤', () => {
    test('應該正確處理 UTM 參數', async () => {
      const { appendToSheet } = require('@/lib/google-sheets');
      appendToSheet.mockResolvedValue(true);

      const dataWithUTM = {
        ...validRegistrationData,
        utmParams: {
          utm_campaign: 'summer_2024',
          utm_source_platform: 'google',
          utm_marketing_tactic: 'search_ads',
          utm_creative_format: 'text'
        }
      };

      const request = new NextRequest('http://localhost:3000/api/event-registration', {
        method: 'POST',
        body: JSON.stringify(dataWithUTM),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // 檢查 Google Sheets 是否收到 UTM 參數
      const appendCall = appendToSheet.mock.calls[0];
      expect(appendCall[1]).toEqual(expect.arrayContaining([
        expect.stringContaining('summer_2024'), // utm_campaign
        expect.stringContaining('google'), // utm_source_platform
      ]));
    });
  });
});
