/**
 * Order Status API 路由測試
 * 測試 /api/order-status 端點的完整功能
 */

import { NextRequest } from 'next/server';
import { POST } from '@/app/api/order-status/route';

// Mock dependencies
jest.mock('@/lib/payuni', () => ({
  queryPayUniOrder: jest.fn(),
  convertTradeStatus: jest.fn(),
  convertPaymentType: jest.fn(),
  getOverallPaymentStatus: jest.fn(),
}));

jest.mock('@/lib/google-sheets', () => ({
  getSheetData: jest.fn(),
}));

// Mock console methods to reduce test noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
});

describe('/api/order-status', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockGoogleSheetsData = [
    // Header row (A-AD columns)
    ['姓名', 'B', 'C', '...', 'V(訂單號碼)', 'W(付款狀態)', '...', 'AB(重新付款訂單號碼)'],
    // Data rows
    ['張三', 'B', 'C', '...', 'pangea_1234567890', '已付款', '...', ''],
    ['李四', 'B', 'C', '...', 'pangea_1234567891', '重新付款中', '...', 'pangea_retry_1234567891'],
    ['王五', 'B', 'C', '...', 'pangea_1234567892', '未付款', '...', ''],
  ];

  const mockPayUniSuccessResponse = {
    Status: 'SUCCESS',
    Message: '查詢成功',
    Result: {
      MerOrderNo: 'pangea_1234567890',
      TradeNo: 'TN123456789',
      TradeAmt: 1500,
      TradeDate: '2024-07-20',
      TradeTime: '14:30:25',
      TradeStatus: '1', // 已付款
      PaymentType: '1', // 信用卡
      CloseStatus: '0',
      RefundStatus: '0',
    }
  };

  describe('成功案例', () => {
    test('應該成功查詢 PayUni 訂單狀態', async () => {
      const { queryPayUniOrder, convertTradeStatus, convertPaymentType, getOverallPaymentStatus } = require('@/lib/payuni');
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);
      queryPayUniOrder.mockResolvedValue(mockPayUniSuccessResponse);
      convertTradeStatus.mockReturnValue('已付款');
      convertPaymentType.mockReturnValue('信用卡');
      getOverallPaymentStatus.mockReturnValue('已付款');

      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.orderNo).toBe('pangea_1234567890');
      expect(data.paymentStatus).toBe('已付款');
      expect(data.source).toBe('payuni');
      expect(queryPayUniOrder).toHaveBeenCalledWith({ orderNo: 'pangea_1234567890' });
    });

    test('應該優先使用 Google Sheets 資料處理重新付款中狀態', async () => {
      const { queryPayUniOrder } = require('@/lib/payuni');
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);

      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567891' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.orderNo).toBe('pangea_1234567891');
      expect(data.paymentStatus).toBe('重新付款中');
      expect(data.source).toBe('google_sheets');
      expect(queryPayUniOrder).not.toHaveBeenCalled(); // 不應該調用 PayUni API
    });

    test('應該在重新付款訂單號碼中找到訂單', async () => {
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);

      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_retry_1234567891' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.orderNo).toBe('pangea_retry_1234567891');
      expect(data.source).toBe('google_sheets');
    });

    test('應該在 Google Sheets 找不到時回退到 PayUni API', async () => {
      const { queryPayUniOrder, convertTradeStatus, convertPaymentType, getOverallPaymentStatus } = require('@/lib/payuni');
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockResolvedValue([]); // 空資料
      queryPayUniOrder.mockResolvedValue(mockPayUniSuccessResponse);
      convertTradeStatus.mockReturnValue('已付款');
      convertPaymentType.mockReturnValue('信用卡');
      getOverallPaymentStatus.mockReturnValue('已付款');

      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_not_in_sheets' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.source).toBe('payuni');
      expect(queryPayUniOrder).toHaveBeenCalledWith({ orderNo: 'pangea_not_in_sheets' });
    });
  });

  describe('驗證錯誤', () => {
    test('應該拒絕空的訂單號碼', async () => {
      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({ orderNo: '' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('請輸入訂單號碼');
    });

    test('應該拒絕缺少訂單號碼的請求', async () => {
      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({}),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('請輸入訂單號碼');
    });

    test('應該拒絕只有空白字符的訂單號碼', async () => {
      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({ orderNo: '   ' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('請輸入訂單號碼');
    });
  });

  describe('錯誤處理', () => {
    test('應該處理找不到訂單的情況', async () => {
      const { queryPayUniOrder } = require('@/lib/payuni');
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockResolvedValue([]); // 空資料
      queryPayUniOrder.mockResolvedValue(null); // PayUni 也找不到

      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'nonexistent_order' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toContain('找不到指定的訂單');
    });

    test('應該處理 PayUni API 查詢失敗', async () => {
      const { queryPayUniOrder } = require('@/lib/payuni');
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);
      queryPayUniOrder.mockResolvedValue({
        Status: 'FAILED',
        Message: 'API 錯誤'
      });

      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.source).toBe('google_sheets'); // 回退到 Google Sheets 資料
    });

    test('應該處理 Google Sheets 查詢失敗', async () => {
      const { queryPayUniOrder } = require('@/lib/payuni');
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockRejectedValue(new Error('Google Sheets API 錯誤'));
      queryPayUniOrder.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toContain('找不到指定的訂單');
    });

    test('應該處理無效的 JSON 請求', async () => {
      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('伺服器錯誤');
    });
  });

  describe('資料來源優先級', () => {
    test('應該優先使用 PayUni 資料（非重新付款中狀態）', async () => {
      const { queryPayUniOrder, convertTradeStatus, convertPaymentType, getOverallPaymentStatus } = require('@/lib/payuni');
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);
      queryPayUniOrder.mockResolvedValue(mockPayUniSuccessResponse);
      convertTradeStatus.mockReturnValue('已付款');
      convertPaymentType.mockReturnValue('信用卡');
      getOverallPaymentStatus.mockReturnValue('已付款');

      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.source).toBe('payuni'); // 優先使用 PayUni 資料
      expect(queryPayUniOrder).toHaveBeenCalled();
    });

    test('應該在 PayUni 失敗時使用 Google Sheets 資料', async () => {
      const { queryPayUniOrder } = require('@/lib/payuni');
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);
      queryPayUniOrder.mockRejectedValue(new Error('PayUni API 錯誤'));

      const request = new NextRequest('http://localhost:3000/api/order-status', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.source).toBe('google_sheets'); // 回退到 Google Sheets
    });
  });
});
