/**
 * API 路由測試工具函數
 * 提供統一的測試輔助功能
 */

import { NextRequest, NextResponse } from 'next/server';

/**
 * 創建模擬的 NextRequest 物件
 */
export function createMockRequest(
  url: string,
  options: {
    method?: string;
    body?: unknown;
    headers?: Record<string, string>;
  } = {}
): NextRequest {
  const { method = 'GET', body, headers = {} } = options;
  
  const requestInit: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  };

  if (body) {
    requestInit.body = typeof body === 'string' ? body : JSON.stringify(body);
  }

  return new NextRequest(url, requestInit);
}

/**
 * 等待指定時間
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 生成測試用的訂單號
 */
export function generateTestOrderNo(): string {
  return `pangea_test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 生成測試用的 PayUni 交易號
 */
export function generateTestTradeNo(): string {
  return `test_payuni_${Date.now()}`;
}

/**
 * 模擬 Google Sheets 資料格式
 */
export function createMockSheetData(rows: string[][]): string[][] {
  const headers = [
    '姓名', '電子郵件', '電話', '場次', '參與類型', 
    '提交時間', '付款狀態', '訂單編號', '金額', 'AC欄位',
    'UTM Source', 'UTM Medium', 'UTM Campaign', 'FBP', 'FBC'
  ];
  
  return [headers, ...rows];
}

/**
 * 模擬 PayUni API 回應
 */
export function createMockPayUniResponse(data: Record<string, unknown>) {
  const defaultResponse = {
    Status: 'SUCCESS',
    Message: '成功',
    'Result[0][MerTradeNo]': 'pangea_test_123',
    'Result[0][TradeNo]': 'test_payuni_456',
    'Result[0][TradeStatus]': '1',
    'Result[0][PaymentType]': '1',
    'Result[0][TradeAmt]': '3000',
    'Result[0][PaymentDay]': '2025-01-01 12:00:00'
  };

  return { ...defaultResponse, ...data };
}

/**
 * 驗證 API 回應格式
 */
export function validateApiResponse(response: Record<string, unknown>, expectedFields: string[]) {
  const errors: string[] = [];

  expectedFields.forEach(field => {
    if (!(field in response)) {
      errors.push(`缺少必要欄位: ${field}`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 設置完整的測試環境變數
 */
export function setupTestEnvironment() {
  const originalEnv = process.env;

  const testEnvVars = {
    // PayUni 測試環境
    PAYUNI_ENVIRONMENT: 'sandbox',
    PAYUNI_SANDBOX_MER_ID: 'S01421169',
    PAYUNI_SANDBOX_HASH_KEY: 'test_hash_key_32_characters_long',
    PAYUNI_SANDBOX_HASH_IV: 'test_hash_iv_16_chars',
    PAYUNI_NOTIFY_URL: 'https://test.ngrok-free.app/api/webhook/payment',
    NEXT_PUBLIC_BASE_URL: 'https://test.pangea.com',

    // Google Sheets 測試環境
    GOOGLE_SHEETS_PRIVATE_KEY: '-----BEGIN PRIVATE KEY-----\ntest_private_key\n-----END PRIVATE KEY-----',
    GOOGLE_SHEETS_CLIENT_EMAIL: '<EMAIL>',
    GOOGLE_SHEETS_SPREADSHEET_ID: 'test_sheet_id_123',
    GOOGLE_SHEETS_WATCH_SPREADSHEET_ID: 'test_watch_sheet_id_456',
    GOOGLE_SHEETS_BLOG_SPREADSHEET_ID: 'test_blog_sheet_id_789',

    // Meta CAPI 測試環境
    META_PIXEL_ID: 'test_pixel_123',
    META_ACCESS_TOKEN: 'test_access_token_456',

    // GTM 測試環境
    NEXT_PUBLIC_GTM_ID: 'GTM-TEST123',
  };

  process.env = { ...originalEnv, ...testEnvVars };

  return () => {
    process.env = originalEnv;
  };
}

/**
 * 模擬環境變數（向後相容）
 */
export function mockEnvironmentVariables(vars: Record<string, string>) {
  const originalEnv = process.env;

  beforeEach(() => {
    process.env = { ...originalEnv, ...vars };
  });

  afterEach(() => {
    process.env = originalEnv;
  });
}

/**
 * 解析 NextResponse 為 JSON
 */
export async function parseResponse(response: NextResponse): Promise<{
  status: number;
  data: unknown;
  headers: Record<string, string>;
}> {
  const status = response.status;
  const headers: Record<string, string> = {};

  response.headers.forEach((value, key) => {
    headers[key] = value;
  });

  let data;
  try {
    const text = await response.text();
    data = text ? JSON.parse(text) : null;
  } catch (error) {
    data = null;
  }

  return { status, data, headers };
}

/**
 * 建立測試用的 API 請求
 */
export function createTestApiRequest(
  url: string,
  options: {
    method?: string;
    body?: unknown;
    headers?: Record<string, string>;
  } = {}
): NextRequest {
  const { method = 'GET', body, headers = {} } = options;

  const requestInit: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  };

  if (body && method !== 'GET') {
    requestInit.body = typeof body === 'string' ? body : JSON.stringify(body);
  }

  return new NextRequest(url, requestInit);
}

/**
 * 驗證 API 錯誤回應格式
 */
export function validateErrorResponse(
  response: { status: number; data?: { error?: string } },
  expectedStatus: number,
  expectedMessage?: string
): boolean {
  if (response.status !== expectedStatus) {
    return false;
  }

  if (expectedMessage && !response.data?.error?.includes(expectedMessage)) {
    return false;
  }

  return true;
}

/**
 * 生成測試用的電子郵件
 */
export function generateTestEmail(domain: string = 'test.com'): string {
  const username = `test_${Math.random().toString(36).substr(2, 8)}`;
  return `${username}@${domain}`;
}

/**
 * 等待指定時間（用於測試異步操作）
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 清理測試資料
 */
export async function cleanupTestData(orderNos: string[]) {
  // 在實際實作中，這裡會清理測試產生的資料
  // 例如從 Google Sheets 中刪除測試記錄
  console.log('清理測試資料:', orderNos);
}

/**
 * 驗證表單資料格式
 */
export function validateFormDataStructure(data: Record<string, unknown>) {
  const requiredFields = ['name', 'email', 'phone', 'sessionTimes', 'participationType'];
  const errors: string[] = [];

  requiredFields.forEach(field => {
    if (!(field in data)) {
      errors.push(`缺少必要欄位: ${field}`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 模擬 Meta CAPI 事件資料
 */
export function createMockCAPIEvent(eventType: string, data: Record<string, unknown> = {}) {
  const baseEvent = {
    event_name: eventType,
    event_time: Math.floor(Date.now() / 1000),
    action_source: 'website',
    event_source_url: 'https://pangea.weaven.co',
    user_data: {
      em: '<EMAIL>',
      ph: '0912345678'
    },
    custom_data: {
      currency: 'TWD',
      value: 3000
    }
  };

  return { ...baseEvent, ...data };
}

/**
 * 驗證日期格式
 */
export function isValidISODate(dateString: string): boolean {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime()) && dateString.includes('T');
}

/**
 * 生成測試用的 UTM 參數
 */
export function generateTestUTMParams() {
  return {
    utmSource: 'test_source',
    utmMedium: 'test_medium',
    utmCampaign: 'test_campaign',
    fbp: 'fb.1.1234567890.1234567890',
    fbc: 'fb.1.1234567890.1234567890'
  };
}

/**
 * 模擬場次可用性資料
 */
export function createMockSessionAvailability() {
  return {
    '台北 07/20（日）13:20': {
      total: 10,
      confirmed: 3,
      reserved: 2,
      available: 5,
      isFull: false
    },
    '台北 07/20（日）15:20': {
      total: 10,
      confirmed: 8,
      reserved: 2,
      available: 0,
      isFull: true
    }
  };
}
