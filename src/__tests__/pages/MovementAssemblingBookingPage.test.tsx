/**
 * 錶匠體驗報名頁面組件測試
 * 測試表單渲染、驗證和提交功能
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { jest } from '@jest/globals';
import MovementAssemblingBookingPage from '@/app/movement-assembling-booking/page';

// Mock Next.js components
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: React.ComponentProps<'img'>) {
    return <img src={src} alt={alt} {...props} />;
  };
});

jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) {
    return <a href={href} {...props}>{children}</a>;
  };
});

// Define types for session availability
interface SessionAvailability {
  registered: number;
  capacity: number;
  available: number;
}

interface AvailabilityData {
  [sessionName: string]: SessionAvailability;
}

// Mock hooks
jest.mock('@/hooks/useSessionAvailability', () => ({
  useSessionAvailability: () => ({
    availability: {
      '台北 07/20（日）13:20': { registered: 5, capacity: 10, available: 5 },
      '台北 07/20（日）15:20': { registered: 8, capacity: 10, available: 2 },
      '台中 07/18（五）19:20': { registered: 3, capacity: 8, available: 5 }
    },
    isLoading: false,
    error: null,
    refetch: jest.fn()
  }),
  isSessionAvailable: (sessionName: string, availability: AvailabilityData) => {
    return availability[sessionName]?.available > 0;
  },
  getAvailabilityText: (sessionName: string, availability: AvailabilityData) => {
    const session = availability[sessionName];
    if (!session) return '';
    if (session.available <= 0) return '已額滿';
    if (session.available <= 3) return `剩餘 ${session.available} 名額`;
    return '名額充足';
  }
}));

// Mock fetch
global.fetch = jest.fn();

describe('MovementAssemblingBookingPage', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ success: true, orderNo: 'pangea_test_123' })
    });
  });

  describe('基本渲染測試', () => {
    test('應該正確渲染頁面標題和描述', () => {
      render(<MovementAssemblingBookingPage />);
      
      expect(screen.getByText('錶匠體驗機芯拆解')).toBeInTheDocument();
      expect(screen.getByText(/深入了解機械錶的精密構造/)).toBeInTheDocument();
    });

    test('應該顯示所有表單欄位', () => {
      render(<MovementAssemblingBookingPage />);
      
      // 檢查基本資料欄位
      expect(screen.getByLabelText(/姓名/)).toBeInTheDocument();
      expect(screen.getByLabelText(/電子郵件/)).toBeInTheDocument();
      expect(screen.getByLabelText(/手機號碼/)).toBeInTheDocument();
      
      // 檢查場次選擇
      expect(screen.getByText('選擇場次')).toBeInTheDocument();
      
      // 檢查參與類型
      expect(screen.getByText('參與類型')).toBeInTheDocument();
      expect(screen.getByLabelText('個人參與')).toBeInTheDocument();
      expect(screen.getByLabelText('雙人參與')).toBeInTheDocument();
    });

    test('應該顯示場次選項和可用性資訊', () => {
      render(<MovementAssemblingBookingPage />);
      
      // 檢查場次選項
      expect(screen.getByText(/台北 07\/20（日）13:20/)).toBeInTheDocument();
      expect(screen.getByText(/台北 07\/20（日）15:20/)).toBeInTheDocument();
      expect(screen.getByText(/台中 07\/18（五）19:20/)).toBeInTheDocument();
      
      // 檢查可用性資訊
      expect(screen.getByText('名額充足')).toBeInTheDocument();
      expect(screen.getByText('剩餘 2 名額')).toBeInTheDocument();
    });

    test('應該顯示同意條款 checkbox', () => {
      render(<MovementAssemblingBookingPage />);
      
      const agreeCheckbox = screen.getByRole('checkbox', { name: /我已詳細閱讀並同意/ });
      expect(agreeCheckbox).toBeInTheDocument();
      expect(agreeCheckbox).not.toBeChecked();
    });

    test('應該顯示提交按鈕', () => {
      render(<MovementAssemblingBookingPage />);
      
      const submitButton = screen.getByRole('button', { name: /確認報名/ });
      expect(submitButton).toBeInTheDocument();
      expect(submitButton).toBeDisabled(); // 初始狀態應該是禁用的
    });
  });

  describe('表單驗證測試', () => {
    test('應該在空表單提交時顯示驗證錯誤', async () => {
      render(<MovementAssemblingBookingPage />);
      
      const submitButton = screen.getByRole('button', { name: /確認報名/ });
      
      // 先同意條款才能啟用提交按鈕
      const agreeCheckbox = screen.getByRole('checkbox', { name: /我已詳細閱讀並同意/ });
      await user.click(agreeCheckbox);
      
      // 提交空表單
      await user.click(submitButton);
      
      // 檢查驗證錯誤訊息
      await waitFor(() => {
        expect(screen.getByTestId('error-name')).toBeInTheDocument();
        expect(screen.getByTestId('error-email')).toBeInTheDocument();
        expect(screen.getByTestId('error-phone')).toBeInTheDocument();
        expect(screen.getByTestId('error-session')).toBeInTheDocument();
      });
    });

    test('應該驗證 email 格式', async () => {
      render(<MovementAssemblingBookingPage />);
      
      const emailInput = screen.getByLabelText(/電子郵件/);
      const agreeCheckbox = screen.getByRole('checkbox', { name: /我已詳細閱讀並同意/ });
      const submitButton = screen.getByRole('button', { name: /確認報名/ });
      
      // 填寫無效的 email
      await user.type(emailInput, 'invalid-email');
      await user.click(agreeCheckbox);
      await user.click(submitButton);
      
      // 檢查 email 驗證錯誤
      await waitFor(() => {
        expect(screen.getByTestId('error-email')).toHaveTextContent(/請輸入有效的電子郵件地址/);
      });
    });

    test('應該驗證電話號碼格式', async () => {
      render(<MovementAssemblingBookingPage />);
      
      const phoneInput = screen.getByLabelText(/手機號碼/);
      const agreeCheckbox = screen.getByRole('checkbox', { name: /我已詳細閱讀並同意/ });
      const submitButton = screen.getByRole('button', { name: /確認報名/ });
      
      // 填寫無效的電話號碼
      await user.type(phoneInput, '123');
      await user.click(agreeCheckbox);
      await user.click(submitButton);
      
      // 檢查電話驗證錯誤
      await waitFor(() => {
        expect(screen.getByTestId('error-phone')).toHaveTextContent(/請輸入有效的手機號碼/);
      });
    });

    test('應該要求選擇場次', async () => {
      render(<MovementAssemblingBookingPage />);
      
      const nameInput = screen.getByLabelText(/姓名/);
      const emailInput = screen.getByLabelText(/電子郵件/);
      const phoneInput = screen.getByLabelText(/手機號碼/);
      const agreeCheckbox = screen.getByRole('checkbox', { name: /我已詳細閱讀並同意/ });
      const submitButton = screen.getByRole('button', { name: /確認報名/ });
      
      // 填寫基本資料但不選擇場次
      await user.type(nameInput, '張測試');
      await user.type(emailInput, '<EMAIL>');
      await user.type(phoneInput, '0912345678');
      await user.click(agreeCheckbox);
      await user.click(submitButton);
      
      // 檢查場次選擇錯誤
      await waitFor(() => {
        expect(screen.getByTestId('error-session')).toHaveTextContent(/請選擇至少一個場次/);
      });
    });
  });

  describe('表單互動測試', () => {
    test('應該能夠填寫基本資料', async () => {
      render(<MovementAssemblingBookingPage />);
      
      const nameInput = screen.getByLabelText(/姓名/) as HTMLInputElement;
      const emailInput = screen.getByLabelText(/電子郵件/) as HTMLInputElement;
      const phoneInput = screen.getByLabelText(/手機號碼/) as HTMLInputElement;
      
      await user.type(nameInput, '張測試');
      await user.type(emailInput, '<EMAIL>');
      await user.type(phoneInput, '0912345678');
      
      expect(nameInput.value).toBe('張測試');
      expect(emailInput.value).toBe('<EMAIL>');
      expect(phoneInput.value).toBe('0912345678');
    });

    test('應該能夠選擇場次', async () => {
      render(<MovementAssemblingBookingPage />);
      
      const sessionCheckbox = screen.getByRole('checkbox', { 
        name: /台北 07\/20（日）13:20/ 
      });
      
      await user.click(sessionCheckbox);
      expect(sessionCheckbox).toBeChecked();
    });

    test('應該能夠選擇參與類型', async () => {
      render(<MovementAssemblingBookingPage />);
      
      const individualRadio = screen.getByRole('radio', { name: '個人參與' });
      const pairRadio = screen.getByRole('radio', { name: '雙人參與' });
      
      // 預設應該選擇個人參與
      expect(individualRadio).toBeChecked();
      expect(pairRadio).not.toBeChecked();
      
      // 切換到雙人參與
      await user.click(pairRadio);
      expect(pairRadio).toBeChecked();
      expect(individualRadio).not.toBeChecked();
    });

    test('雙人參與時應該顯示同伴資料欄位', async () => {
      render(<MovementAssemblingBookingPage />);
      
      const pairRadio = screen.getByRole('radio', { name: '雙人參與' });
      await user.click(pairRadio);
      
      // 檢查同伴資料欄位出現
      await waitFor(() => {
        expect(screen.getByLabelText(/同伴姓名/)).toBeInTheDocument();
        expect(screen.getByLabelText(/同伴電子郵件/)).toBeInTheDocument();
      });
    });

    test('同意條款後應該啟用提交按鈕', async () => {
      render(<MovementAssemblingBookingPage />);
      
      const agreeCheckbox = screen.getByRole('checkbox', { name: /我已詳細閱讀並同意/ });
      const submitButton = screen.getByRole('button', { name: /確認報名/ });
      
      // 初始狀態按鈕應該禁用
      expect(submitButton).toBeDisabled();
      
      // 同意條款後按鈕應該啟用
      await user.click(agreeCheckbox);
      expect(submitButton).toBeEnabled();
    });
  });

  describe('表單提交測試', () => {
    test('應該成功提交完整表單', async () => {
      render(<MovementAssemblingBookingPage />);
      
      // 填寫完整表單
      await user.type(screen.getByLabelText(/姓名/), '張測試');
      await user.type(screen.getByLabelText(/電子郵件/), '<EMAIL>');
      await user.type(screen.getByLabelText(/手機號碼/), '0912345678');
      
      // 選擇場次
      await user.click(screen.getByRole('checkbox', { 
        name: /台北 07\/20（日）13:20/ 
      }));
      
      // 同意條款
      await user.click(screen.getByRole('checkbox', { name: /我已詳細閱讀並同意/ }));
      
      // 提交表單
      await user.click(screen.getByRole('button', { name: /確認報名/ }));
      
      // 驗證 API 調用
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/event-registration', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining('張測試')
        });
      });
    });

    test('應該處理提交錯誤', async () => {
      // Mock API 錯誤
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        json: async () => ({ success: false, error: '提交失敗' })
      });

      render(<MovementAssemblingBookingPage />);
      
      // 填寫並提交表單
      await user.type(screen.getByLabelText(/姓名/), '張測試');
      await user.type(screen.getByLabelText(/電子郵件/), '<EMAIL>');
      await user.type(screen.getByLabelText(/手機號碼/), '0912345678');
      await user.click(screen.getByRole('checkbox', { 
        name: /台北 07\/20（日）13:20/ 
      }));
      await user.click(screen.getByRole('checkbox', { name: /我已詳細閱讀並同意/ }));
      await user.click(screen.getByRole('button', { name: /確認報名/ }));
      
      // 檢查錯誤訊息顯示
      await waitFor(() => {
        expect(screen.getByText(/提交失敗/)).toBeInTheDocument();
      });
    });
  });

  describe('響應式設計測試', () => {
    test('應該在行動裝置上正確顯示', () => {
      // 模擬行動裝置螢幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<MovementAssemblingBookingPage />);
      
      // 檢查表單在小螢幕上的佈局 - 使用表單元素查詢
      const nameInput = screen.getByLabelText(/姓名/);
      expect(nameInput).toBeInTheDocument();

      // 檢查按鈕在行動版的樣式
      const submitButton = screen.getByRole('button', { name: /確認報名/ });
      expect(submitButton).toBeInTheDocument();
    });
  });
});
